#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة إدارة المهام والمواعيد وزيارة أولياء الأمور والاجتماعات
"""

import sys
import os
import sqlite3
import traceback
from datetime import datetime, date
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class TasksAndAppointmentsWindow(QMainWindow):
    """نافذة إدارة المهام والمواعيد"""
    
    def __init__(self, parent=None, db_path="data.db"):
        super().__init__(parent)
        print(f"🔍 [DEBUG] تهيئة نافذة إدارة المهام والمواعيد...")
        
        self.db_path = db_path
        self.current_edit_id = None

        # ذاكرة الحقول لتوفير الوقت
        self.last_title = ""
        self.last_description = ""
        self.last_notes = ""
        
        try:
            # إنشاء قاعدة البيانات والجداول
            print(f"🔍 [DEBUG] إنشاء قاعدة البيانات...")
            self.init_database()
            print(f"✅ [SUCCESS] تم إنشاء قاعدة البيانات بنجاح")
            
            # إعداد واجهة المستخدم
            print(f"🔍 [DEBUG] إعداد واجهة المستخدم...")
            self.setupUI()
            print(f"✅ [SUCCESS] تم إعداد واجهة المستخدم بنجاح")
            
            # تحميل البيانات
            print(f"🔍 [DEBUG] تحميل البيانات...")
            self.load_data()
            print(f"✅ [SUCCESS] تم تحميل البيانات بنجاح")

            # تحميل القيم المحفوظة
            self.load_last_values()
            
            print(f"✅ [SUCCESS] تم تهيئة نافذة إدارة المهام والمواعيد بنجاح")
            
        except Exception as e:
            print(f"❌ [ERROR] خطأ في تهيئة نافذة إدارة المهام والمواعيد: {str(e)}")
            traceback.print_exc()
            raise

    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # جدول المهام
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS tasks_appointments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    description TEXT,
                    task_type TEXT NOT NULL,
                    priority TEXT NOT NULL,
                    status TEXT NOT NULL,
                    start_date DATE NOT NULL,
                    end_date DATE,
                    start_time TIME,
                    end_time TIME,
                    location TEXT,
                    attendees TEXT,
                    notes TEXT,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
            conn.close()
            print(f"✅ [SUCCESS] تم إنشاء جداول قاعدة البيانات بنجاح")
            
        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء قاعدة البيانات: {str(e)}")
            traceback.print_exc()
            raise

    def setupUI(self):
        """إعداد واجهة المستخدم بتصميم جميل وأنيق"""
        try:
            self.setWindowTitle("📋 إدارة المهام والمواعيد")
            self.setLayoutDirection(Qt.RightToLeft)

            # تطبيق تنسيق جميل وأنيق مشابه لـ attendance_processing_window
            self.setStyleSheet("""
                QMainWindow {
                    background-color: #f8f9fa;
                    font-family: 'Calibri';
                }

                QTabWidget::pane {
                    border: 2px solid #2c3e50;
                    border-radius: 10px;
                    background-color: white;
                    margin-top: -1px;
                }

                QTabWidget::tab-bar {
                    alignment: center;
                }

                QTabBar::tab {
                    background: qlineargradient(
                        x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #ecf0f1,
                        stop: 1 #bdc3c7
                    );
                    border: 2px solid #2c3e50;
                    border-bottom-color: #2c3e50;
                    border-top-left-radius: 10px;
                    border-top-right-radius: 10px;
                    min-width: 180px;
                    height: 45px;
                    padding: 10px 20px;
                    margin-right: 3px;
                    font-family: 'Calibri';
                    font-size: 20px;
                    font-weight: bold;
                    color: #2c3e50;
                }

                QTabBar::tab:selected {
                    background: qlineargradient(
                        x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #3498db,
                        stop: 1 #2980b9
                    );
                    color: white;
                    border-bottom-color: white;
                    height: 50px;
                    font-size: 16px;
                    font-weight: bold;
                }

                QTabBar::tab:hover:!selected {
                    background: qlineargradient(
                        x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #5dade2,
                        stop: 1 #3498db
                    );
                    color: white;
                }

                QGroupBox {
                    font-family: 'Calibri';
                    font-size: 14px;
                    font-weight: bold;
                    border: 2px solid #2c3e50;
                    border-radius: 8px;
                    margin-top: 10px;
                    padding-top: 10px;
                    background-color: #f8f9fa;
                }

                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 10px 0 10px;
                    color: #2c3e50;
                    font-weight: bold;
                }

                QLabel {
                    font-family: 'Calibri';
                    font-size: 14px;
                    font-weight: bold;
                    color: #2c3e50;
                }

                QLineEdit, QComboBox, QDateEdit, QTimeEdit, QTextEdit {
                    font-family: 'Calibri';
                    font-size: 13px;
                    font-weight: bold;
                    padding: 8px;
                    border: 2px solid #bdc3c7;
                    border-radius: 6px;
                    background-color: white;
                    color: #2c3e50;
                }

                QLineEdit:focus, QComboBox:focus, QDateEdit:focus, QTimeEdit:focus, QTextEdit:focus {
                    border: 2px solid #3498db;
                    background-color: #f8f9fa;
                }
            """)

            # إنشاء التبويبات مباشرة
            self.create_tabs()

            print(f"✅ [SUCCESS] تم إعداد واجهة المستخدم بنجاح")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إعداد واجهة المستخدم: {str(e)}")
            traceback.print_exc()
            raise

    def showEvent(self, event):
        """عند إظهار النافذة - تعظيمها تلقائياً"""
        super().showEvent(event)
        self.showMaximized()

    def create_tabs(self):
        """إنشاء التبويبات الرئيسية"""
        try:
            # إنشاء عنصر التبويبات
            self.tab_widget = QTabWidget()
            self.setCentralWidget(self.tab_widget)

            # التبويب الأول: إضافة مهمة/موعد جديد
            self.add_task_tab = QWidget()
            self.create_add_task_tab()
            self.tab_widget.addTab(self.add_task_tab, "➕ إضافة مهمة/موعد")

            # التبويب الثاني: عرض وإدارة المهام
            self.manage_tasks_tab = QWidget()
            self.create_manage_tasks_tab()
            self.tab_widget.addTab(self.manage_tasks_tab, "📋 إدارة المهام")

            # التبويب الثالث: التقارير
            self.reports_tab = QWidget()
            self.create_reports_tab()
            self.tab_widget.addTab(self.reports_tab, "📊 التقارير")

            print(f"✅ [SUCCESS] تم إنشاء التبويبات بنجاح")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء التبويبات: {str(e)}")
            traceback.print_exc()
            raise

    def create_add_task_tab(self):
        """إنشاء تبويب إضافة مهمة/موعد جديد"""
        try:
            layout = QGridLayout(self.add_task_tab)

            # عنوان المهمة/الموعد
            title_label = QLabel("العنوان:")
            title_label.setFont(QFont("Calibri", 14, QFont.Bold))
            title_label.setStyleSheet("color: #1e3a8a; font-weight: bold;")
            self.title_input = QLineEdit()
            self.title_input.setFont(QFont("Calibri", 13, QFont.Bold))
            self.title_input.setStyleSheet("color: black; font-weight: bold;")

            # نوع المهمة
            type_label = QLabel("النوع:")
            type_label.setFont(QFont("Calibri", 14, QFont.Bold))
            type_label.setStyleSheet("color: #1e3a8a; font-weight: bold;")
            self.type_combo = QComboBox()
            self.type_combo.addItems([
                "مهمة عادية", "زيارة ولي أمر", "اجتماع",
                 "نشاط مدرسي", "اجتماع أولياء أمور",
                "تقييم التلاميذ", "أخرى"
            ])
            self.type_combo.setFont(QFont("Calibri", 13, QFont.Bold))
            self.type_combo.setStyleSheet("color: black; font-weight: bold;")

            # الأولوية
            priority_label = QLabel("الأولوية:")
            priority_label.setFont(QFont("Calibri", 14, QFont.Bold))
            priority_label.setStyleSheet("color: #1e3a8a; font-weight: bold;")
            self.priority_combo = QComboBox()
            self.priority_combo.addItems(["عالية", "متوسطة", "منخفضة"])
            self.priority_combo.setFont(QFont("Calibri", 13, QFont.Bold))
            self.priority_combo.setStyleSheet("color: black; font-weight: bold;")

            # الحالة
            status_label = QLabel("الحالة:")
            status_label.setFont(QFont("Calibri", 14, QFont.Bold))
            status_label.setStyleSheet("color: #1e3a8a; font-weight: bold;")
            self.status_combo = QComboBox()
            self.status_combo.addItems(["مجدولة", "قيد التنفيذ", "مكتملة", "ملغاة", "مؤجلة"])
            self.status_combo.setFont(QFont("Calibri", 13, QFont.Bold))
            self.status_combo.setStyleSheet("color: black; font-weight: bold;")

            # تاريخ البداية
            start_date_label = QLabel("تاريخ البداية:")
            start_date_label.setFont(QFont("Calibri", 14, QFont.Bold))
            start_date_label.setStyleSheet("color: #1e3a8a; font-weight: bold;")
            self.start_date = QDateEdit()
            self.start_date.setDate(QDate.currentDate())
            self.start_date.setCalendarPopup(True)
            self.start_date.setFont(QFont("Calibri", 13, QFont.Bold))
            self.start_date.setStyleSheet("color: black; font-weight: bold;")

            # تاريخ النهاية
            end_date_label = QLabel("تاريخ النهاية:")
            end_date_label.setFont(QFont("Calibri", 14, QFont.Bold))
            end_date_label.setStyleSheet("color: #1e3a8a; font-weight: bold;")
            self.end_date = QDateEdit()
            self.end_date.setDate(QDate.currentDate())
            self.end_date.setCalendarPopup(True)
            self.end_date.setFont(QFont("Calibri", 13, QFont.Bold))
            self.end_date.setStyleSheet("color: black; font-weight: bold;")

            # وقت البداية
            start_time_label = QLabel("وقت البداية:")
            start_time_label.setFont(QFont("Calibri", 14, QFont.Bold))
            start_time_label.setStyleSheet("color: #1e3a8a; font-weight: bold;")
            self.start_time = QTimeEdit()
            self.start_time.setTime(QTime.currentTime())
            self.start_time.setFont(QFont("Calibri", 13, QFont.Bold))
            self.start_time.setStyleSheet("color: black; font-weight: bold;")

            # وقت النهاية
            end_time_label = QLabel("وقت النهاية:")
            end_time_label.setFont(QFont("Calibri", 14, QFont.Bold))
            end_time_label.setStyleSheet("color: #1e3a8a; font-weight: bold;")
            self.end_time = QTimeEdit()
            self.end_time.setTime(QTime.currentTime().addSecs(3600))
            self.end_time.setFont(QFont("Calibri", 13, QFont.Bold))
            self.end_time.setStyleSheet("color: black; font-weight: bold;")

            # المكان
            location_label = QLabel("المكان:")
            location_label.setFont(QFont("Calibri", 14, QFont.Bold))
            location_label.setStyleSheet("color: #1e3a8a; font-weight: bold;")
            self.location_input = QLineEdit()
            self.location_input.setFont(QFont("Calibri", 13, QFont.Bold))
            self.location_input.setStyleSheet("color: black; font-weight: bold;")

            # الحضور
            attendees_label = QLabel("الحضور:")
            attendees_label.setFont(QFont("Calibri", 14, QFont.Bold))
            attendees_label.setStyleSheet("color: #1e3a8a; font-weight: bold;")
            self.attendees_input = QLineEdit()
            self.attendees_input.setFont(QFont("Calibri", 13, QFont.Bold))
            self.attendees_input.setStyleSheet("color: black; font-weight: bold;")

            # الوصف
            description_label = QLabel("الوصف:")
            description_label.setFont(QFont("Calibri", 14, QFont.Bold))
            description_label.setStyleSheet("color: #1e3a8a; font-weight: bold;")
            self.description_input = QTextEdit()
            self.description_input.setFont(QFont("Calibri", 13, QFont.Bold))
            self.description_input.setStyleSheet("color: black; font-weight: bold;")
            self.description_input.setMaximumHeight(80)

            # ملاحظات
            notes_label = QLabel("ملاحظات:")
            notes_label.setFont(QFont("Calibri", 14, QFont.Bold))
            notes_label.setStyleSheet("color: #1e3a8a; font-weight: bold;")
            self.notes_input = QTextEdit()
            self.notes_input.setFont(QFont("Calibri", 13, QFont.Bold))
            self.notes_input.setStyleSheet("color: black; font-weight: bold;")
            self.notes_input.setMaximumHeight(60)

            # ترتيب العناصر
            layout.addWidget(title_label, 0, 0)
            layout.addWidget(self.title_input, 0, 1, 1, 3)

            layout.addWidget(type_label, 1, 0)
            layout.addWidget(self.type_combo, 1, 1)
            layout.addWidget(priority_label, 1, 2)
            layout.addWidget(self.priority_combo, 1, 3)

            layout.addWidget(status_label, 2, 0)
            layout.addWidget(self.status_combo, 2, 1)
            layout.addWidget(location_label, 2, 2)
            layout.addWidget(self.location_input, 2, 3)

            layout.addWidget(start_date_label, 3, 0)
            layout.addWidget(self.start_date, 3, 1)
            layout.addWidget(end_date_label, 3, 2)
            layout.addWidget(self.end_date, 3, 3)

            layout.addWidget(start_time_label, 4, 0)
            layout.addWidget(self.start_time, 4, 1)
            layout.addWidget(end_time_label, 4, 2)
            layout.addWidget(self.end_time, 4, 3)

            layout.addWidget(attendees_label, 5, 0)
            layout.addWidget(self.attendees_input, 5, 1, 1, 3)

            layout.addWidget(description_label, 6, 0)
            layout.addWidget(self.description_input, 6, 1, 1, 3)

            layout.addWidget(notes_label, 7, 0)
            layout.addWidget(self.notes_input, 7, 1, 1, 3)

            # الأزرار
            self.add_button = QPushButton("➕ إضافة المهمة/الموعد")
            self.add_button.setFont(QFont("Calibri", 13, QFont.Bold))
            self.add_button.setStyleSheet("""
                QPushButton {
                    background-color: #27ae60;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 20px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #229954;
                }
            """)
            self.add_button.clicked.connect(self.add_new_task)

            self.update_button = QPushButton("✏️ تحديث المهمة/الموعد")
            self.update_button.setFont(QFont("Calibri", 13, QFont.Bold))
            self.update_button.setStyleSheet("""
                QPushButton {
                    background-color: #f39c12;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 20px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #e67e22;
                }
            """)
            self.update_button.clicked.connect(self.update_task)
            self.update_button.setVisible(False)

            self.cancel_edit_button = QPushButton("❌ إلغاء التعديل")
            self.cancel_edit_button.setFont(QFont("Calibri", 13, QFont.Bold))
            self.cancel_edit_button.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 20px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)
            self.cancel_edit_button.clicked.connect(self.cancel_edit)
            self.cancel_edit_button.setVisible(False)

            self.clear_button = QPushButton("🗑️ مسح الحقول")
            self.clear_button.setFont(QFont("Calibri", 13, QFont.Bold))
            self.clear_button.setStyleSheet("""
                QPushButton {
                    background-color: #95a5a6;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 20px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #7f8c8d;
                }
            """)
            self.clear_button.clicked.connect(self.clear_form)

            layout.addWidget(self.add_button, 8, 0)
            layout.addWidget(self.update_button, 8, 1)
            layout.addWidget(self.cancel_edit_button, 8, 2)
            layout.addWidget(self.clear_button, 8, 3)

            print(f"✅ [SUCCESS] تم إنشاء تبويب إضافة المهام بنجاح")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء تبويب إضافة المهام: {str(e)}")
            traceback.print_exc()
            raise

    def create_manage_tasks_tab(self):
        """إنشاء تبويب إدارة المهام"""
        try:
            layout = QVBoxLayout(self.manage_tasks_tab)

            # أزرار التحكم
            controls_layout = QHBoxLayout()

            refresh_btn = QPushButton("🔄 تحديث البيانات")
            refresh_btn.setFont(QFont("Calibri", 13, QFont.Bold))
            refresh_btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 20px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)
            refresh_btn.clicked.connect(self.load_data)

            edit_btn = QPushButton("✏️ تعديل المحدد")
            edit_btn.setFont(QFont("Calibri", 13, QFont.Bold))
            edit_btn.setStyleSheet("""
                QPushButton {
                    background-color: #f39c12;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 20px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #e67e22;
                }
            """)
            edit_btn.clicked.connect(self.edit_selected_task)

            delete_btn = QPushButton("🗑️ حذف المحدد")
            delete_btn.setFont(QFont("Calibri", 13, QFont.Bold))
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 20px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)
            delete_btn.clicked.connect(self.delete_selected_task)

            report_btn = QPushButton("📋 تقرير المهام")
            report_btn.setFont(QFont("Calibri", 13, QFont.Bold))
            report_btn.setStyleSheet("""
                QPushButton {
                    background-color: #9b59b6;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 20px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #8e44ad;
                }
            """)
            report_btn.clicked.connect(self.generate_tasks_report)

            # زر طباعة المهمة المحددة
            print_task_btn = QPushButton("🖨️ طباعة المهمة")
            print_task_btn.setFont(QFont("Calibri", 13, QFont.Bold))
            print_task_btn.setStyleSheet("""
                QPushButton {
                    background-color: #16a085;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 20px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #138d75;
                }
            """)
            print_task_btn.clicked.connect(self.print_selected_task)

            controls_layout.addWidget(refresh_btn)
            controls_layout.addWidget(edit_btn)
            controls_layout.addWidget(delete_btn)
            controls_layout.addWidget(print_task_btn)
            controls_layout.addWidget(report_btn)

            layout.addLayout(controls_layout)

            # الجدول
            self.tasks_table = QTableWidget()
            self.setup_tasks_table()
            layout.addWidget(self.tasks_table)

            print(f"✅ [SUCCESS] تم إنشاء تبويب إدارة المهام بنجاح")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء تبويب إدارة المهام: {str(e)}")
            traceback.print_exc()
            raise

    def setup_tasks_table(self):
        """إعداد جدول المهام"""
        try:
            columns = [
                "ID", "العنوان", "النوع", "الأولوية", "الحالة",
                "تاريخ البداية", "تاريخ النهاية", "وقت البداية", "وقت النهاية",
                "المكان", "الحضور", "تاريخ الإنشاء"
            ]

            self.tasks_table.setColumnCount(len(columns))
            self.tasks_table.setHorizontalHeaderLabels(columns)

            # تطبيق تنسيق جميل للجدول
            self.tasks_table.setStyleSheet("""
                QTableWidget {
                    background-color: #ffffff;
                    border: 2px solid #bdc3c7;
                    border-radius: 8px;
                    gridline-color: #ecf0f1;
                    font-family: 'Calibri';
                    selection-background-color: #e8f4fd;
                }

                QHeaderView::section {
                    background-color: #87CEEB;
                    color: #2C3E50;
                    font-weight: bold;
                    font-family: 'Calibri';
                    font-size: 16px;
                    padding: 8px;
                    border: 1px solid #5DADE2;
                    text-align: center;
                    height: 25px;
                    min-height: 25px;
                }

                QTableWidget::item {
                    padding: 8px;
                    border-bottom: 1px solid #ecf0f1;
                    font-family: 'Calibri';
                }

                QTableWidget::item:selected {
                    background-color: #e8f4fd;
                    color: #2c3e50;
                }
            """)

            # إعدادات الجدول
            self.tasks_table.setAlternatingRowColors(True)
            self.tasks_table.setSelectionBehavior(QAbstractItemView.SelectRows)
            self.tasks_table.setSelectionMode(QAbstractItemView.SingleSelection)
            self.tasks_table.setSortingEnabled(True)

            # تطبيق الخط على رأس الجدول بلون أزرق سماوي وارتفاع 25 نقطة مع خط Calibri 16 أسود غامق
            header = self.tasks_table.horizontalHeader()
            header.setFont(QFont("Calibri", 16, QFont.Bold))
            header.setDefaultSectionSize(25)  # ارتفاع 25 نقطة
            header.setMinimumSectionSize(25)  # الحد الأدنى للارتفاع
            header.setSectionResizeMode(QHeaderView.ResizeToContents)

            self.tasks_table.verticalHeader().setDefaultSectionSize(40)
            self.tasks_table.verticalHeader().setVisible(False)

            print(f"✅ [SUCCESS] تم إعداد جدول المهام بنجاح")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إعداد جدول المهام: {str(e)}")
            traceback.print_exc()
            raise

    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        try:
            layout = QGridLayout(self.reports_tab)

            # عنوان التبويب
            title_label = QLabel("تقارير المهام والمواعيد")
            title_label.setFont(QFont("Calibri", 14, QFont.Bold))
            title_label.setStyleSheet("color: #1e3a8a; font-weight: bold;")
            title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(title_label, 0, 0, 1, 2)

            # أزرار التقارير
            type_report_btn = QPushButton("📂 تقرير حسب النوع")
            type_report_btn.setFont(QFont("Calibri", 13, QFont.Bold))
            type_report_btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 15px 25px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)
            type_report_btn.clicked.connect(self.generate_type_report)

            priority_report_btn = QPushButton("⭐ تقرير حسب الأولوية")
            priority_report_btn.setFont(QFont("Calibri", 13, QFont.Bold))
            priority_report_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e67e22;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 15px 25px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #d35400;
                }
            """)
            priority_report_btn.clicked.connect(self.generate_priority_report)

            status_report_btn = QPushButton("📊 تقرير حسب الحالة")
            status_report_btn.setFont(QFont("Calibri", 13, QFont.Bold))
            status_report_btn.setStyleSheet("""
                QPushButton {
                    background-color: #27ae60;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 15px 25px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #229954;
                }
            """)
            status_report_btn.clicked.connect(self.generate_status_report)

            comprehensive_report_btn = QPushButton("📋 تقرير شامل")
            comprehensive_report_btn.setFont(QFont("Calibri", 13, QFont.Bold))
            comprehensive_report_btn.setStyleSheet("""
                QPushButton {
                    background-color: #9b59b6;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 15px 25px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #8e44ad;
                }
            """)
            comprehensive_report_btn.clicked.connect(self.generate_comprehensive_report)

            layout.addWidget(type_report_btn, 1, 0)
            layout.addWidget(priority_report_btn, 1, 1)
            layout.addWidget(status_report_btn, 2, 0)
            layout.addWidget(comprehensive_report_btn, 2, 1)

            print(f"✅ [SUCCESS] تم إنشاء تبويب التقارير بنجاح")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء تبويب التقارير: {str(e)}")
            traceback.print_exc()
            raise

    def save_last_values(self):
        """حفظ القيم الأخيرة للحقول المهمة"""
        try:
            self.last_title = self.title_input.text().strip()
            self.last_description = self.description_input.toPlainText().strip()
            self.last_notes = self.notes_input.toPlainText().strip()
            print(f"💾 [DEBUG] تم حفظ القيم الأخيرة")
        except Exception as e:
            print(f"❌ [ERROR] خطأ في حفظ القيم: {str(e)}")

    def load_last_values(self):
        """تحميل القيم المحفوظة في الحقول"""
        try:
            if self.last_title:
                self.title_input.setText(self.last_title)
            if self.last_description:
                self.description_input.setPlainText(self.last_description)
            if self.last_notes:
                self.notes_input.setPlainText(self.last_notes)
            print(f"📥 [DEBUG] تم تحميل القيم المحفوظة")
        except Exception as e:
            print(f"❌ [ERROR] خطأ في تحميل القيم: {str(e)}")

    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            print(f"🔍 [DEBUG] تحميل بيانات المهام...")

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, title, task_type, priority, status, start_date, end_date,
                       start_time, end_time, location, attendees, created_date
                FROM tasks_appointments
                ORDER BY start_date DESC, start_time DESC
            """)

            tasks = cursor.fetchall()
            conn.close()

            # تعيين عدد الصفوف
            self.tasks_table.setRowCount(len(tasks))

            # ملء الجدول بالبيانات
            for row_index, task in enumerate(tasks):
                for col_index, value in enumerate(task):
                    item = QTableWidgetItem(str(value) if value is not None else "")
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFont(QFont("Calibri", 13, QFont.Bold))

                    # تلوين جميل حسب الأولوية
                    if col_index == 3:  # الأولوية
                        if value == "عالية":
                            item.setBackground(QColor("#ffebee"))
                            item.setForeground(QColor("#c62828"))
                        elif value == "متوسطة":
                            item.setBackground(QColor("#fff3e0"))
                            item.setForeground(QColor("#ef6c00"))
                        else:
                            item.setBackground(QColor("#e8f5e8"))
                            item.setForeground(QColor("#2e7d32"))

                    # تلوين جميل حسب الحالة
                    elif col_index == 4:  # الحالة
                        if value == "مكتملة":
                            item.setBackground(QColor("#e8f5e8"))
                            item.setForeground(QColor("#2e7d32"))
                        elif value == "قيد التنفيذ":
                            item.setBackground(QColor("#e3f2fd"))
                            item.setForeground(QColor("#1565c0"))
                        elif value == "ملغاة":
                            item.setBackground(QColor("#ffebee"))
                            item.setForeground(QColor("#c62828"))
                        elif value == "مؤجلة":
                            item.setBackground(QColor("#fff3e0"))
                            item.setForeground(QColor("#ef6c00"))
                        else:
                            item.setForeground(QColor("black"))
                    else:
                        item.setForeground(QColor("black"))

                    self.tasks_table.setItem(row_index, col_index, item)

            print(f"✅ [SUCCESS] تم تحميل {len(tasks)} مهمة/موعد")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في تحميل البيانات: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات: {str(e)}")

    def add_new_task(self):
        """إضافة مهمة/موعد جديد"""
        try:
            # التحقق من صحة البيانات
            if not self.title_input.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال عنوان المهمة/الموعد.")
                return

            # جمع البيانات
            title = self.title_input.text().strip()
            description = self.description_input.toPlainText().strip()
            task_type = self.type_combo.currentText()
            priority = self.priority_combo.currentText()
            status = self.status_combo.currentText()
            start_date = self.start_date.date().toString("yyyy-MM-dd")
            end_date = self.end_date.date().toString("yyyy-MM-dd")
            start_time = self.start_time.time().toString("HH:mm")
            end_time = self.end_time.time().toString("HH:mm")
            location = self.location_input.text().strip()
            attendees = self.attendees_input.text().strip()
            notes = self.notes_input.toPlainText().strip()

            # إدراج في قاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO tasks_appointments
                (title, description, task_type, priority, status, start_date, end_date,
                 start_time, end_time, location, attendees, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (title, description, task_type, priority, status, start_date, end_date,
                  start_time, end_time, location, attendees, notes))

            conn.commit()
            conn.close()

            print(f"✅ [SUCCESS] تم إضافة المهمة/الموعد: {title}")
            QMessageBox.information(self, "نجح", "تم إضافة المهمة/الموعد بنجاح.")

            # حفظ القيم المهمة قبل المسح
            self.save_last_values()

            # مسح الحقول وتحديث البيانات
            self.clear_form()
            self.load_data()

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إضافة المهمة: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة المهمة: {str(e)}")

    def clear_form(self):
        """مسح جميع الحقول"""
        try:
            self.title_input.clear()
            self.description_input.clear()
            self.type_combo.setCurrentIndex(0)
            self.priority_combo.setCurrentIndex(0)
            self.status_combo.setCurrentIndex(0)
            self.start_date.setDate(QDate.currentDate())
            self.end_date.setDate(QDate.currentDate())
            self.start_time.setTime(QTime.currentTime())
            self.end_time.setTime(QTime.currentTime().addSecs(3600))
            self.location_input.clear()
            self.attendees_input.clear()
            self.notes_input.clear()

            # إخفاء أزرار التعديل وإظهار زر الإضافة
            self.add_button.setVisible(True)
            self.update_button.setVisible(False)
            self.cancel_edit_button.setVisible(False)

            self.current_edit_id = None

            print(f"✅ [SUCCESS] تم مسح الحقول")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في مسح الحقول: {str(e)}")

    def edit_selected_task(self):
        """تعديل المهمة المحددة"""
        try:
            current_row = self.tasks_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد مهمة للتعديل.")
                return

            # الحصول على ID المهمة
            task_id = int(self.tasks_table.item(current_row, 0).text())

            # جلب بيانات المهمة من قاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT title, description, task_type, priority, status, start_date, end_date,
                       start_time, end_time, location, attendees, notes
                FROM tasks_appointments WHERE id = ?
            """, (task_id,))

            task_data = cursor.fetchone()
            conn.close()

            if not task_data:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على بيانات المهمة.")
                return

            # ملء الحقول بالبيانات
            self.title_input.setText(task_data[0] or "")
            self.description_input.setPlainText(task_data[1] or "")

            # تعيين القيم في القوائم المنسدلة
            type_index = self.type_combo.findText(task_data[2] or "")
            if type_index >= 0:
                self.type_combo.setCurrentIndex(type_index)

            priority_index = self.priority_combo.findText(task_data[3] or "")
            if priority_index >= 0:
                self.priority_combo.setCurrentIndex(priority_index)

            status_index = self.status_combo.findText(task_data[4] or "")
            if status_index >= 0:
                self.status_combo.setCurrentIndex(status_index)

            # تعيين التواريخ والأوقات
            if task_data[5]:
                self.start_date.setDate(QDate.fromString(task_data[5], "yyyy-MM-dd"))
            if task_data[6]:
                self.end_date.setDate(QDate.fromString(task_data[6], "yyyy-MM-dd"))
            if task_data[7]:
                self.start_time.setTime(QTime.fromString(task_data[7], "HH:mm"))
            if task_data[8]:
                self.end_time.setTime(QTime.fromString(task_data[8], "HH:mm"))

            self.location_input.setText(task_data[9] or "")
            self.attendees_input.setText(task_data[10] or "")
            self.notes_input.setPlainText(task_data[11] or "")

            # تعيين وضع التعديل
            self.current_edit_id = task_id
            self.add_button.setVisible(False)
            self.update_button.setVisible(True)
            self.cancel_edit_button.setVisible(True)

            # الانتقال لتبويب الإضافة
            self.tab_widget.setCurrentIndex(0)

            print(f"✅ [SUCCESS] تم تحميل بيانات المهمة للتعديل: {task_id}")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في تعديل المهمة: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في تعديل المهمة: {str(e)}")

    def update_task(self):
        """تحديث المهمة المحددة"""
        try:
            if not self.current_edit_id:
                QMessageBox.warning(self, "خطأ", "لا توجد مهمة محددة للتحديث.")
                return

            # التحقق من صحة البيانات
            if not self.title_input.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال عنوان المهمة/الموعد.")
                return

            # جمع البيانات
            title = self.title_input.text().strip()
            description = self.description_input.toPlainText().strip()
            task_type = self.type_combo.currentText()
            priority = self.priority_combo.currentText()
            status = self.status_combo.currentText()
            start_date = self.start_date.date().toString("yyyy-MM-dd")
            end_date = self.end_date.date().toString("yyyy-MM-dd")
            start_time = self.start_time.time().toString("HH:mm")
            end_time = self.end_time.time().toString("HH:mm")
            location = self.location_input.text().strip()
            attendees = self.attendees_input.text().strip()
            notes = self.notes_input.toPlainText().strip()

            # تحديث في قاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                UPDATE tasks_appointments SET
                title = ?, description = ?, task_type = ?, priority = ?, status = ?,
                start_date = ?, end_date = ?, start_time = ?, end_time = ?,
                location = ?, attendees = ?, notes = ?, updated_date = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (title, description, task_type, priority, status, start_date, end_date,
                  start_time, end_time, location, attendees, notes, self.current_edit_id))

            conn.commit()
            conn.close()

            print(f"✅ [SUCCESS] تم تحديث المهمة: {title}")
            QMessageBox.information(self, "نجح", "تم تحديث المهمة/الموعد بنجاح.")

            # مسح الحقول وتحديث البيانات
            self.clear_form()
            self.load_data()

            # العودة لتبويب الإدارة
            self.tab_widget.setCurrentIndex(1)

        except Exception as e:
            print(f"❌ [ERROR] خطأ في تحديث المهمة: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في تحديث المهمة: {str(e)}")

    def cancel_edit(self):
        """إلغاء التعديل"""
        self.clear_form()
        print(f"✅ [SUCCESS] تم إلغاء التعديل")

    def delete_selected_task(self):
        """حذف المهمة المحددة"""
        try:
            current_row = self.tasks_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد مهمة للحذف.")
                return

            # الحصول على ID والعنوان
            task_id = int(self.tasks_table.item(current_row, 0).text())
            task_title = self.tasks_table.item(current_row, 1).text()

            # تأكيد الحذف
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف المهمة:\n'{task_title}'؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # حذف من قاعدة البيانات
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                cursor.execute("DELETE FROM tasks_appointments WHERE id = ?", (task_id,))

                conn.commit()
                conn.close()

                print(f"✅ [SUCCESS] تم حذف المهمة: {task_title}")
                QMessageBox.information(self, "نجح", "تم حذف المهمة/الموعد بنجاح.")

                # تحديث البيانات
                self.load_data()

        except Exception as e:
            print(f"❌ [ERROR] خطأ في حذف المهمة: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في حذف المهمة: {str(e)}")

    def print_selected_task(self):
        """طباعة المهمة المحددة مع خيار نوع الطباعة"""
        try:
            current_row = self.tasks_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد مهمة للطباعة.")
                return

            # الحصول على ID المهمة
            task_id = int(self.tasks_table.item(current_row, 0).text())

            # جلب بيانات المهمة من قاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT title, description, task_type, priority, status, start_date, end_date,
                       start_time, end_time, location, attendees, notes, created_date
                FROM tasks_appointments WHERE id = ?
            """, (task_id,))

            task_data = cursor.fetchone()
            conn.close()

            if not task_data:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على بيانات المهمة.")
                return

            # إظهار نافذة اختيار نوع الطباعة
            self.show_print_options_dialog(task_data, task_id)

        except Exception as e:
            print(f"❌ [ERROR] خطأ في طباعة المهمة: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة المهمة: {str(e)}")

    def show_print_options_dialog(self, task_data, task_id):
        """إظهار نافذة خيارات الطباعة"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QRadioButton, QButtonGroup

            dialog = QDialog(self)
            dialog.setWindowTitle("خيارات الطباعة")
            dialog.setFixedSize(400, 200)
            dialog.setLayoutDirection(Qt.RightToLeft)

            layout = QVBoxLayout(dialog)

            # عنوان النافذة
            title_label = QLabel("اختر نوع الطباعة:")
            title_label.setFont(QFont("Calibri", 14, QFont.Bold))
            title_label.setStyleSheet("color: #1e3a8a; font-weight: bold; margin: 10px;")
            layout.addWidget(title_label)

            # مجموعة أزرار الاختيار
            button_group = QButtonGroup()

            # خيار الطباعة الحرارية
            thermal_radio = QRadioButton("🖨️ طباعة حرارية (مثل وصل الأداء)")
            thermal_radio.setFont(QFont("Calibri", 13, QFont.Bold))
            thermal_radio.setStyleSheet("color: black; font-weight: bold; margin: 5px;")
            thermal_radio.setChecked(True)  # الخيار الافتراضي
            button_group.addButton(thermal_radio, 1)
            layout.addWidget(thermal_radio)

            # خيار طباعة PDF
            pdf_radio = QRadioButton("📄 طباعة PDF")
            pdf_radio.setFont(QFont("Calibri", 13, QFont.Bold))
            pdf_radio.setStyleSheet("color: black; font-weight: bold; margin: 5px;")
            button_group.addButton(pdf_radio, 2)
            layout.addWidget(pdf_radio)

            # الأزرار
            buttons_layout = QHBoxLayout()

            ok_button = QPushButton("✅ طباعة")
            ok_button.setFont(QFont("Calibri", 13, QFont.Bold))
            ok_button.setStyleSheet("""
                QPushButton {
                    background-color: #27ae60;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 20px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #229954;
                }
            """)

            cancel_button = QPushButton("❌ إلغاء")
            cancel_button.setFont(QFont("Calibri", 13, QFont.Bold))
            cancel_button.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 20px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)

            buttons_layout.addWidget(ok_button)
            buttons_layout.addWidget(cancel_button)
            layout.addLayout(buttons_layout)

            # ربط الأزرار
            ok_button.clicked.connect(lambda: self.execute_print_choice(dialog, button_group, task_data, task_id))
            cancel_button.clicked.connect(dialog.reject)

            dialog.exec_()

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إظهار نافذة خيارات الطباعة: {str(e)}")
            traceback.print_exc()

    def execute_print_choice(self, dialog, button_group, task_data, task_id):
        """تنفيذ خيار الطباعة المحدد"""
        try:
            choice = button_group.checkedId()
            dialog.accept()

            if choice == 1:  # طباعة حرارية
                self.create_thermal_task_receipt(task_data, task_id)
            elif choice == 2:  # طباعة PDF
                self.create_task_receipt(task_data, task_id)

        except Exception as e:
            print(f"❌ [ERROR] خطأ في تنفيذ خيار الطباعة: {str(e)}")
            traceback.print_exc()

    def create_task_receipt(self, task_data, task_id):
        """إنشاء وصل المهمة مثل وصل الأداء"""
        try:
            from fpdf import FPDF
            import arabic_reshaper
            from bidi.algorithm import get_display

            class ArabicPDF(FPDF):
                def __init__(self):
                    super().__init__('P','mm','A4')
                    self.set_margins(10, 10, 10)
                    fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')

                    calibri_path = os.path.join(fonts_dir, 'calibri.ttf')
                    calibri_bold_path = os.path.join(fonts_dir, 'calibrib.ttf')
                    arial_path = os.path.join(fonts_dir, 'arial.ttf')
                    arial_bold_path = os.path.join(fonts_dir, 'arialbd.ttf')

                    if os.path.exists(calibri_path):
                        self.add_font('Calibri', '', calibri_path)
                        self.calibri_available = True
                    else:
                        self.calibri_available = False

                    if os.path.exists(calibri_bold_path):
                        self.add_font('Calibri', 'B', calibri_bold_path)
                        self.calibri_bold_available = True
                    else:
                        self.calibri_bold_available = False

                    if os.path.exists(arial_path):
                        self.add_font('Arial', '', arial_path)
                    if os.path.exists(arial_bold_path):
                        self.add_font('Arial', 'B', arial_bold_path)

                def set_title_font(self, size=14):
                    if self.calibri_bold_available:
                        self.set_font('Calibri', 'B', size)
                    else:
                        self.set_font('Arial', 'B', size)

                def set_detail_font(self, size=13):
                    if self.calibri_bold_available:
                        self.set_font('Calibri', 'B', size)
                    else:
                        self.set_font('Arial', 'B', size)

                def ar_text(self, txt: str) -> str:
                    reshaped = arabic_reshaper.reshape(str(txt))
                    return get_display(reshaped)

            # الحصول على بيانات المؤسسة
            logo_path, institution_name = self.get_institution_data()

            # إنشاء مجلد التقارير
            desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop')
            reports_dir = os.path.join(desktop_path, 'تقارير المهام والمواعيد')
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)

            # إنشاء ملف PDF
            pdf = ArabicPDF()
            pdf.add_page()
            y = pdf.get_y()

            # إضافة الشعار
            if logo_path:
                logo_w, logo_h = 60, 24
                x_logo = (pdf.w - logo_w) / 2
                pdf.image(logo_path, x=x_logo, y=y, w=logo_w, h=logo_h)
                y += logo_h + 5

            # اسم المؤسسة
            pdf.set_title_font(14)
            pdf.set_text_color(0, 51, 102)
            pdf.set_xy(10, y)
            pdf.cell(0, 10, pdf.ar_text(institution_name), border=0, align='C')
            y += 15

            # عنوان الوصل
            pdf.set_title_font(16)
            pdf.set_text_color(0, 51, 102)
            pdf.set_xy(10, y)
            pdf.cell(0, 12, pdf.ar_text("وصل مهمة/موعد"), border=1, align='C')
            y += 20

            # رقم المهمة وتاريخ الإنشاء
            pdf.set_detail_font(12)
            pdf.set_text_color(0, 0, 0)
            pdf.set_xy(10, y)
            pdf.cell(95, 8, pdf.ar_text(f"رقم المهمة: {task_id}"), border=1, align='C')
            pdf.set_xy(105, y)
            pdf.cell(95, 8, pdf.ar_text(f"تاريخ الإنشاء: {task_data[12] or 'غير محدد'}"), border=1, align='C')
            y += 15

            # تفاصيل المهمة
            pdf.set_detail_font(13)

            details = [
                ("العنوان", task_data[0]),
                ("الوصف", task_data[1] or "غير محدد"),
                ("النوع", task_data[2]),
                ("الأولوية", task_data[3]),
                ("الحالة", task_data[4]),
                ("تاريخ البداية", task_data[5]),
                ("تاريخ النهاية", task_data[6] or "غير محدد"),
                ("وقت البداية", task_data[7] or "غير محدد"),
                ("وقت النهاية", task_data[8] or "غير محدد"),
                ("المكان", task_data[9] or "غير محدد"),
                ("الحضور", task_data[10] or "غير محدد"),
                ("ملاحظات", task_data[11] or "غير محدد")
            ]

            for label, value in details:
                pdf.set_xy(10, y)
                pdf.cell(150, 8, pdf.ar_text(str(value)), border=1, align='R')
                pdf.set_xy(160, y)
                pdf.cell(40, 8, pdf.ar_text(f"{label}:"), border=1, align='C')
                y += 8

            # مساحة للتوقيع
            y += 20
            pdf.set_xy(10, y)
            pdf.cell(95, 15, '', border=1, align='C')
            pdf.set_xy(105, y)
            pdf.cell(95, 15, '', border=1, align='C')

            pdf.set_xy(10, y + 17)
            pdf.cell(95, 8, pdf.ar_text('توقيع المسؤول'), border=0, align='C')
            pdf.set_xy(105, y + 17)
            pdf.cell(95, 8, pdf.ar_text('تاريخ التوقيع'), border=0, align='C')

            # حفظ الملف
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = os.path.join(reports_dir, f"task_receipt_{task_id}_{timestamp}.pdf")
            pdf.output(filename)

            print(f"✅ [SUCCESS] تم إنشاء وصل المهمة: {filename}")
            QMessageBox.information(self, "نجح", f"تم إنشاء وصل المهمة بنجاح:\n{filename}")

            # فتح الملف
            try:
                os.startfile(filename)
            except Exception as e:
                print(f"⚠️ فشل في فتح الملف: {e}")
                QMessageBox.information(self, "تنبيه", f"تم إنشاء الوصل في:\n{filename}")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء وصل المهمة: {str(e)}")
            traceback.print_exc()
            raise

    def create_thermal_task_receipt(self, task_data, task_id):
        """إنشاء وصل المهمة للطباعة الحرارية (مثل وصل الأداء تماماً)"""
        try:
            # إنشاء محتوى الوصل مثل وصل الأداء
            receipt_content = self.create_task_receipt_content(task_data, task_id)

            # طباعة على الطابعة الحرارية
            self.send_to_thermal_printer(receipt_content)

            print(f"✅ [SUCCESS] تم إرسال وصل المهمة للطباعة الحرارية بنجاح")
            QMessageBox.information(self, "نجح", "تم إرسال وصل المهمة للطباعة الحرارية بنجاح.")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في الطباعة الحرارية للمهمة: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في الطباعة الحرارية: {str(e)}")

    def create_task_receipt_content(self, task_data, task_id):
        """إنشاء محتوى وصل المهمة مثل وصل الأداء تماماً"""
        try:
            # الحصول على بيانات المؤسسة
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT المؤسسة, رقم_الهاتف, المدينة FROM بيانات_المؤسسة LIMIT 1")
            institution_data = cursor.fetchone()
            conn.close()

            institution_name = institution_data[0] if institution_data and institution_data[0] else "المؤسسة التعليمية"
            institution_phone = institution_data[1] if institution_data and institution_data[1] else ""
            institution_city = institution_data[2] if institution_data and institution_data[2] else ""

            # بناء محتوى الوصل - تصميم مطابق لوصل الأداء
            receipt_lines = []

            # إطار حول اسم المؤسسة والمدينة
            receipt_lines.append("=" * 40)
            receipt_lines.append(f"{institution_name}".center(40))
            if institution_city:
                receipt_lines.append(f"{institution_city}".center(40))
            receipt_lines.append("=" * 40)

            # الهاتف خارج الإطار
            if institution_phone:
                receipt_lines.append(f"هاتف: {institution_phone}".center(40))

            # وصل المهمة في الوسط
            receipt_lines.append("وصل المهمة".center(40))

            # تفاصيل المهمة - محاذاة إلى اليمين
            receipt_lines.append(f"العنوان: {task_data[0]}".rjust(40))
            receipt_lines.append(f"النوع: {task_data[2]}".rjust(40))
            receipt_lines.append(f"الأولوية: {task_data[3]}".rjust(40))
            receipt_lines.append(f"الحالة: {task_data[4]}".rjust(40))
            receipt_lines.append(f"تاريخ البداية: {task_data[5]}".rjust(40))

            if task_data[6]:  # تاريخ النهاية
                receipt_lines.append(f"تاريخ النهاية: {task_data[6]}".rjust(40))

            if task_data[7]:  # وقت البداية
                receipt_lines.append(f"وقت البداية: {task_data[7]}".rjust(40))

            if task_data[8]:  # وقت النهاية
                receipt_lines.append(f"وقت النهاية: {task_data[8]}".rjust(40))

            if task_data[9]:  # المكان
                receipt_lines.append(f"المكان: {task_data[9]}".rjust(40))

            if task_data[10]:  # الحضور
                receipt_lines.append(f"الحضور: {task_data[10]}".rjust(40))

            if task_data[1]:  # الوصف
                receipt_lines.append(f"الوصف: {task_data[1]}".rjust(40))

            if task_data[11]:  # ملاحظات
                receipt_lines.append(f"ملاحظات: {task_data[11]}".rjust(40))

            receipt_lines.append(f"رقم المهمة: {task_id}".rjust(40))
            receipt_lines.append(f"تاريخ الإنشاء: {task_data[12] or 'غير محدد'}".rjust(40))

            receipt_lines.append(f"تاريخ الطباعة {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}".rjust(40))
            receipt_lines.append("")  # سطر فارغ
            receipt_lines.append("نتمنى لكم التوفيق والنجاح".center(40))

            return "\n".join(receipt_lines)

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء محتوى وصل المهمة: {str(e)}")
            traceback.print_exc()
            return "خطأ في إنشاء وصل المهمة"

    def send_to_thermal_printer(self, content):
        """إرسال المحتوى للطابعة الحرارية (نفس الطريقة المستخدمة في وصل الأداء)"""
        try:
            print(f"🔍 [DEBUG] إرسال المحتوى للطابعة الحرارية...")

            # الحصول على اسم الطابعة الحرارية من قاعدة البيانات
            thermal_printer = self.get_thermal_printer_name()

            if not thermal_printer:
                QMessageBox.warning(self, "تحذير", "لم يتم تعيين طابعة حرارية. يرجى الذهاب إلى إعدادات الطابعة أولاً.")
                return

            # طباعة مباشرة على الطابعة الحرارية بدون حفظ ملف
            self.print_directly_to_thermal_printer(thermal_printer, content)

            print(f"✅ [SUCCESS] تم إرسال وصل المهمة للطابعة الحرارية: {thermal_printer}")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إرسال المحتوى للطابعة: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ في الطباعة", f"فشل في الطباعة على الطابعة الحرارية:\n{str(e)}")

    def get_thermal_printer_name(self):
        """الحصول على اسم الطابعة الحرارية من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT الطابعة_الحرارية FROM إعدادات_الطابعة LIMIT 1")
            result = cursor.fetchone()

            conn.close()

            if result and result[0]:
                return result[0]
            else:
                return None

        except Exception as e:
            print(f"❌ [ERROR] خطأ في الحصول على اسم الطابعة الحرارية: {str(e)}")
            return None

    def print_directly_to_thermal_printer(self, printer_name, content):
        """طباعة مباشرة على الطابعة الحرارية (نفس الطريقة المستخدمة في وصل الأداء)"""
        try:
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QPainter, QFont, QFontMetrics, QPen
            from PyQt5.QtCore import QSizeF, QRect, Qt

            # إعداد الطابعة
            printer = QPrinter()
            printer.setPrinterName(printer_name)

            # إعداد حجم الورقة للطابعة الحرارية (80mm)
            printer.setPageSize(QPrinter.Custom)
            printer.setPageSizeMM(QSizeF(75, 170))  # عرض 75 ارتفاع 170

            # إعداد الهوامش - 0.4 من كل الجهات
            printer.setPageMargins(0.4, 0.4, 0.4, 0.4, QPrinter.Millimeter)

            # إعداد الرسام
            painter = QPainter()
            if painter.begin(printer):
                # إعداد الخط Calibri 12 أسود غامق
                font = QFont("Calibri", 12, QFont.Bold)
                painter.setFont(font)
                painter.setPen(Qt.black)

                # حساب مقاييس الخط
                font_metrics = QFontMetrics(font)
                line_height = font_metrics.height() + 4  # إضافة مسافة بين الأسطر

                # تقسيم المحتوى إلى أسطر
                lines = content.split('\n')

                # رسم الوصل باستخدام جدول منظم
                y_position = 20  # البدء من الأعلى
                page_width = printer.pageRect().width() - 20  # عرض الصفحة مع الهوامش

                # حساب عرض الجدول
                table_width = int(page_width)  # عرض الجدول
                table_x = 10  # موضع بداية الجدول

                # متغيرات لتجميع بيانات الجدول
                table_data = []
                table_start_y = None
                in_table_section = False

                for i, line in enumerate(lines):
                    line = line.strip()

                    # تجاهل الأسطر الفارغة
                    if not line:
                        continue
                    elif line.strip().startswith('='):
                        # تجاهل خطوط الإطار
                        continue
                    elif line.strip().startswith('-'):
                        # تجاهل الخطوط المتقطعة
                        continue

                    if ('وصل المهمة' in line or
                        'شكراً لكم' in line or
                        (i < 5 and not ':' in line and not 'تاريخ الطباعة' in line)):

                        # إذا كنا في قسم الجدول، ارسم الجدول أولاً
                        if table_data and table_start_y is not None:
                            y_position = self.draw_table(painter, table_data, table_start_y, page_width, line_height, font_metrics)
                            table_data = []
                            table_start_y = None
                            in_table_section = False

                        # رسم العناوين في الوسط
                        text_width = font_metrics.width(line)
                        x_position = int((page_width - text_width) / 2 + 10)
                        x_position = max(10, x_position)

                        painter.drawText(x_position, int(y_position), line)
                        y_position += line_height

                    elif ':' in line and not line.strip().startswith('تاريخ الطباعة'):
                        # تجميع البيانات لرسم جدول واحد
                        if not in_table_section:
                            table_start_y = y_position
                            in_table_section = True

                        parts = line.split(':', 1)
                        if len(parts) == 2:
                            label = parts[0].strip()
                            value = parts[1].strip()
                            table_data.append((label, value))

                    elif line.strip().startswith('تاريخ الطباعة'):
                        # إذا كنا في قسم الجدول، ارسم الجدول أولاً
                        if table_data and table_start_y is not None:
                            y_position = self.draw_table(painter, table_data, table_start_y, page_width, line_height, font_metrics)
                            table_data = []
                            table_start_y = None
                            in_table_section = False

                        # رسم تاريخ الطباعة في الوسط
                        text_width = font_metrics.width(line)
                        x_position = int((page_width - text_width) / 2 + 10)
                        x_position = max(10, x_position)

                        painter.drawText(x_position, int(y_position), line)
                        y_position += line_height

                # رسم الجدول الأخير إذا كان متبقي
                if table_data and table_start_y is not None:
                    y_position = self.draw_table(painter, table_data, table_start_y, page_width, line_height, font_metrics)

                painter.end()
                print(f"✅ [SUCCESS] تم إرسال وصل المهمة للطباعة على: {printer_name}")
            else:
                raise Exception(f"فشل في بدء الطباعة على الطابعة: {printer_name}")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في الطباعة المباشرة: {str(e)}")
            raise

    def draw_table(self, painter, table_data, start_y, page_width, line_height, font_metrics):
        """رسم جدول حقيقي بخطوط وحدود (نفس الطريقة المستخدمة في وصل الأداء)"""
        try:
            from PyQt5.QtGui import QPen
            from PyQt5.QtCore import QRect, Qt

            if not table_data:
                return start_y

            # حساب أبعاد الجدول
            table_width = int(page_width - 20)
            label_width = int(table_width * 0.4)  # 40% للتسمية
            value_width = int(table_width * 0.6)  # 60% للقيمة
            row_height = line_height + 6

            # حساب ارتفاع الجدول الكامل
            table_height = len(table_data) * row_height

            # رسم إطار الجدول الخارجي
            table_rect = QRect(10, int(start_y), table_width, table_height)
            painter.drawRect(table_rect)

            # رسم الصفوف والأعمدة
            current_y = start_y
            for i, (label, value) in enumerate(table_data):
                # رسم خط أفقي (إلا للصف الأول)
                if i > 0:
                    painter.drawLine(10, int(current_y), 10 + table_width, int(current_y))

                # رسم خط عمودي فاصل بين العمودين
                painter.drawLine(10 + value_width, int(current_y), 10 + value_width, int(current_y + row_height))

                # رسم النص - القيمة في اليسار والتسمية في اليمين
                value_rect = QRect(15, int(current_y + 3), value_width - 10, row_height - 6)
                painter.drawText(value_rect, Qt.AlignRight | Qt.AlignVCenter, value)

                label_rect = QRect(15 + value_width, int(current_y + 3), label_width - 10, row_height - 6)
                painter.drawText(label_rect, Qt.AlignCenter | Qt.AlignVCenter, label)

                current_y += row_height

            # إرجاع الموضع Y الجديد بعد الجدول
            return start_y + table_height + line_height

        except Exception as e:
            print(f"❌ [ERROR] خطأ في رسم الجدول: {str(e)}")
            return start_y

    def generate_tasks_report(self):
        """إنشاء تقرير المهام"""
        try:
            print(f"🔍 [DEBUG] إنشاء تقرير المهام...")

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT title, task_type, priority, status, start_date, end_date, location
                FROM tasks_appointments
                ORDER BY start_date DESC
            """)

            tasks = cursor.fetchall()
            conn.close()

            if not tasks:
                QMessageBox.information(self, "تنبيه", "لا توجد مهام لإنشاء التقرير.")
                return

            # إنشاء التقرير
            self.create_tasks_pdf_report(tasks, "تقرير المهام والمواعيد")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء تقرير المهام: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير: {str(e)}")

    def generate_type_report(self):
        """إنشاء تقرير حسب النوع"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT task_type, COUNT(*) as count
                FROM tasks_appointments
                GROUP BY task_type
                ORDER BY count DESC
            """)

            data = cursor.fetchall()
            conn.close()

            if not data:
                QMessageBox.information(self, "تنبيه", "لا توجد بيانات لإنشاء التقرير.")
                return

            # إنشاء التقرير
            self.create_summary_pdf_report(data, "تقرير المهام حسب النوع", ["النوع", "العدد"])

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء تقرير النوع: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير: {str(e)}")

    def generate_priority_report(self):
        """إنشاء تقرير حسب الأولوية"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT priority, COUNT(*) as count
                FROM tasks_appointments
                GROUP BY priority
                ORDER BY
                    CASE priority
                        WHEN 'عالية' THEN 1
                        WHEN 'متوسطة' THEN 2
                        WHEN 'منخفضة' THEN 3
                    END
            """)

            data = cursor.fetchall()
            conn.close()

            if not data:
                QMessageBox.information(self, "تنبيه", "لا توجد بيانات لإنشاء التقرير.")
                return

            # إنشاء التقرير
            self.create_summary_pdf_report(data, "تقرير المهام حسب الأولوية", ["الأولوية", "العدد"])

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء تقرير الأولوية: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير: {str(e)}")

    def generate_status_report(self):
        """إنشاء تقرير حسب الحالة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT status, COUNT(*) as count
                FROM tasks_appointments
                GROUP BY status
                ORDER BY count DESC
            """)

            data = cursor.fetchall()
            conn.close()

            if not data:
                QMessageBox.information(self, "تنبيه", "لا توجد بيانات لإنشاء التقرير.")
                return

            # إنشاء التقرير
            self.create_summary_pdf_report(data, "تقرير المهام حسب الحالة", ["الحالة", "العدد"])

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء تقرير الحالة: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير: {str(e)}")

    def generate_comprehensive_report(self):
        """إنشاء تقرير شامل"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT title, description, task_type, priority, status,
                       start_date, end_date, start_time, end_time, location, attendees
                FROM tasks_appointments
                ORDER BY start_date DESC, start_time DESC
            """)

            tasks = cursor.fetchall()
            conn.close()

            if not tasks:
                QMessageBox.information(self, "تنبيه", "لا توجد مهام لإنشاء التقرير.")
                return

            # إنشاء التقرير الشامل
            self.create_comprehensive_pdf_report(tasks)

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء التقرير الشامل: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير: {str(e)}")

    def get_institution_data(self):
        """الحصول على بيانات المؤسسة من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT ImagePath1, المؤسسة FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()

            conn.close()

            if result:
                logo_path = result[0] if result[0] and os.path.exists(result[0]) else None
                institution_name = result[1] or "المؤسسة التعليمية"
                return logo_path, institution_name
            else:
                return None, "المؤسسة التعليمية"

        except Exception as e:
            print(f"خطأ في الحصول على بيانات المؤسسة: {str(e)}")
            return None, "المؤسسة التعليمية"

    def create_tasks_pdf_report(self, tasks, title):
        """إنشاء تقرير PDF للمهام بتصميم أنيق مثل print111.py"""
        try:
            from fpdf import FPDF
            import arabic_reshaper
            from bidi.algorithm import get_display

            class ArabicPDF(FPDF):
                def __init__(self):
                    super().__init__('P','mm','A4')
                    self.set_margins(10, 10, 10)
                    self.set_auto_page_break(auto=True, margin=20)
                    fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')

                    # إضافة خطوط Calibri
                    calibri_path = os.path.join(fonts_dir, 'calibri.ttf')
                    calibri_bold_path = os.path.join(fonts_dir, 'calibrib.ttf')

                    if os.path.exists(calibri_path):
                        self.add_font('Calibri', '', calibri_path)
                        self.calibri_available = True
                    else:
                        self.calibri_available = False

                    if os.path.exists(calibri_bold_path):
                        self.add_font('Calibri', 'B', calibri_bold_path)
                        self.calibri_bold_available = True
                    else:
                        self.calibri_bold_available = False

                    # إضافة خطوط Arial كبديل
                    arial_path = os.path.join(fonts_dir, 'arial.ttf')
                    arial_bold_path = os.path.join(fonts_dir, 'arialbd.ttf')

                    if os.path.exists(arial_path):
                        self.add_font('Arial', '', arial_path)
                    if os.path.exists(arial_bold_path):
                        self.add_font('Arial', 'B', arial_bold_path)

                def set_title_font(self, size=14):
                    """خط العناوين - Calibri 14 أزرق غامق"""
                    if self.calibri_bold_available:
                        self.set_font('Calibri', 'B', size)
                    else:
                        self.set_font('Arial', 'B', size)

                def set_detail_font(self, size=13):
                    """خط الجداول - Calibri 13 أسود غامق"""
                    if self.calibri_bold_available:
                        self.set_font('Calibri', 'B', size)
                    else:
                        self.set_font('Arial', 'B', size)

                def ar_text(self, txt: str) -> str:
                    """تحويل النص العربي"""
                    reshaped = arabic_reshaper.reshape(str(txt))
                    return get_display(reshaped)

            # الحصول على بيانات المؤسسة
            logo_path, institution_name = self.get_institution_data()

            # إنشاء مجلد التقارير على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop')
            reports_dir = os.path.join(desktop_path, 'تقارير المهام والمواعيد')
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)

            # إنشاء ملف PDF
            pdf = ArabicPDF()
            pdf.add_page()
            y = pdf.get_y()

            # إضافة الشعار
            if logo_path:
                logo_w, logo_h = 60, 24  # حجم الشعار
                x_logo = (pdf.w - logo_w) / 2
                pdf.image(logo_path, x=x_logo, y=y, w=logo_w, h=logo_h)
                y += logo_h + 5

            # اسم المؤسسة
            pdf.set_title_font(14)  # Calibri 14 أزرق غامق
            pdf.set_text_color(0, 51, 102)  # أزرق غامق
            pdf.set_xy(10, y)
            pdf.cell(0, 10, pdf.ar_text(institution_name), border=0, align='C')
            y += 15

            # عنوان التقرير
            pdf.set_title_font(14)  # Calibri 14 أزرق غامق
            pdf.set_text_color(0, 51, 102)  # أزرق غامق
            pdf.set_xy(10, y)
            pdf.cell(0, 12, pdf.ar_text(title), border=1, align='C')
            pdf.set_text_color(0, 0, 0)  # أسود
            y += 17

            # رؤوس الجدول
            headers = ["العنوان", "النوع", "الأولوية", "الحالة", "تاريخ البداية", "المكان"]
            col_widths = [50, 30, 25, 25, 30, 30]

            pdf.set_detail_font(13)  # Calibri 13 أسود غامق للجداول
            pdf.set_fill_color(200, 220, 255)  # خلفية زرقاء فاتحة

            x = 10
            for i, header in enumerate(headers):
                pdf.set_xy(x, y)
                pdf.cell(col_widths[i], 10, pdf.ar_text(header), border=1, align='C', fill=True)
                x += col_widths[i]
            y += 10

            # بيانات المهام
            pdf.set_detail_font(13)  # Calibri 13 أسود غامق للجداول

            for row_idx, task in enumerate(tasks):
                # تلوين متناوب للصفوف
                if row_idx % 2 == 0:
                    pdf.set_fill_color(245, 250, 255)  # أزرق فاتح جداً
                else:
                    pdf.set_fill_color(255, 255, 255)  # أبيض

                x = 10
                for i, value in enumerate(task[:6]):
                    text = str(value) if value is not None else ""
                    pdf.set_xy(x, y)
                    pdf.cell(col_widths[i], 8, pdf.ar_text(text), border=1, align='C', fill=True)
                    x += col_widths[i]
                y += 8

            # إضافة التوقيع والتاريخ
            y += 10
            current_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # تاريخ الطباعة
            pdf.set_detail_font(10)
            pdf.set_xy(10, y)
            pdf.cell(95, 8, pdf.ar_text(f'تاريخ الطباعة: {current_date}'), border=0, align='R')

            # مساحة للتوقيع
            signature_width = 95
            pdf.set_xy(105, y + 10)
            pdf.cell(signature_width, 15, '', border=1, align='C')

            pdf.set_xy(105, y + 27)
            pdf.cell(signature_width, 8, pdf.ar_text('توقيع المسؤول'), border=0, align='C')

            # حفظ الملف
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = os.path.join(reports_dir, f"tasks_report_{timestamp}.pdf")
            pdf.output(filename)

            print(f"✅ [SUCCESS] تم إنشاء التقرير: {filename}")
            QMessageBox.information(self, "نجح", f"تم إنشاء التقرير بنجاح:\n{filename}")

            # فتح الملف
            try:
                os.startfile(filename)
            except Exception as e:
                print(f"⚠️ فشل في فتح الملف: {e}")
                QMessageBox.information(self, "تنبيه", f"تم إنشاء التقرير في:\n{filename}")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء تقرير PDF: {str(e)}")
            traceback.print_exc()
            raise

    def create_summary_pdf_report(self, data, title, headers):
        """إنشاء تقرير ملخص PDF بتصميم أنيق مثل print111.py"""
        try:
            from fpdf import FPDF
            import arabic_reshaper
            from bidi.algorithm import get_display

            class ArabicPDF(FPDF):
                def __init__(self):
                    super().__init__('P','mm','A4')
                    self.set_margins(10, 10, 10)
                    self.set_auto_page_break(auto=True, margin=20)
                    fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')

                    calibri_path = os.path.join(fonts_dir, 'calibri.ttf')
                    calibri_bold_path = os.path.join(fonts_dir, 'calibrib.ttf')
                    arial_path = os.path.join(fonts_dir, 'arial.ttf')
                    arial_bold_path = os.path.join(fonts_dir, 'arialbd.ttf')

                    if os.path.exists(calibri_path):
                        self.add_font('Calibri', '', calibri_path)
                        self.calibri_available = True
                    else:
                        self.calibri_available = False

                    if os.path.exists(calibri_bold_path):
                        self.add_font('Calibri', 'B', calibri_bold_path)
                        self.calibri_bold_available = True
                    else:
                        self.calibri_bold_available = False

                    if os.path.exists(arial_path):
                        self.add_font('Arial', '', arial_path)
                    if os.path.exists(arial_bold_path):
                        self.add_font('Arial', 'B', arial_bold_path)

                def set_title_font(self, size=14):
                    """خط العناوين - Calibri 14 أزرق غامق"""
                    if self.calibri_bold_available:
                        self.set_font('Calibri', 'B', size)
                    else:
                        self.set_font('Arial', 'B', size)

                def set_detail_font(self, size=13):
                    """خط الجداول - Calibri 13 أسود غامق"""
                    if self.calibri_bold_available:
                        self.set_font('Calibri', 'B', size)
                    else:
                        self.set_font('Arial', 'B', size)

                def ar_text(self, txt: str) -> str:
                    reshaped = arabic_reshaper.reshape(str(txt))
                    return get_display(reshaped)

            # الحصول على بيانات المؤسسة
            logo_path, institution_name = self.get_institution_data()

            # إنشاء مجلد التقارير على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop')
            reports_dir = os.path.join(desktop_path, 'تقارير المهام والمواعيد')
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)

            # إنشاء ملف PDF
            pdf = ArabicPDF()
            pdf.add_page()
            y = pdf.get_y()

            # إضافة الشعار
            if logo_path:
                logo_w, logo_h = 60, 24
                x_logo = (pdf.w - logo_w) / 2
                pdf.image(logo_path, x=x_logo, y=y, w=logo_w, h=logo_h)
                y += logo_h + 5

            # اسم المؤسسة
            pdf.set_title_font(14)  # Calibri 14 أزرق غامق
            pdf.set_text_color(0, 51, 102)
            pdf.set_xy(10, y)
            pdf.cell(0, 10, pdf.ar_text(institution_name), border=0, align='C')
            y += 15

            # عنوان التقرير
            pdf.set_title_font(14)  # Calibri 14 أزرق غامق
            pdf.set_text_color(0, 51, 102)
            pdf.set_xy(10, y)
            pdf.cell(0, 12, pdf.ar_text(title), border=1, align='C')
            pdf.set_text_color(0, 0, 0)
            y += 17

            # رؤوس الجدول
            col_widths = [100, 50]
            pdf.set_detail_font(13)  # Calibri 13 أسود غامق للجداول
            pdf.set_fill_color(200, 220, 255)

            x = 10
            for i, header in enumerate(headers):
                pdf.set_xy(x, y)
                pdf.cell(col_widths[i], 10, pdf.ar_text(header), border=1, align='C', fill=True)
                x += col_widths[i]
            y += 10

            # البيانات
            pdf.set_detail_font(13)  # Calibri 13 أسود غامق للجداول
            total = 0

            for row_idx, row in enumerate(data):
                if row_idx % 2 == 0:
                    pdf.set_fill_color(245, 250, 255)
                else:
                    pdf.set_fill_color(255, 255, 255)

                x = 10
                for i, value in enumerate(row):
                    if i == 1:  # العدد
                        total += int(value)
                        text = str(value)
                    else:
                        text = str(value)
                    pdf.set_xy(x, y)
                    pdf.cell(col_widths[i], 8, pdf.ar_text(text), border=1, align='C', fill=True)
                    x += col_widths[i]
                y += 8

            # الإجمالي
            y += 10
            total_text = f"الإجمالي: {total}"
            pdf.set_title_font(14)  # Calibri 14 أزرق غامق
            pdf.set_text_color(0, 51, 102)
            pdf.set_xy(10, y)
            pdf.cell(0, 10, pdf.ar_text(total_text), border=0, align='C')
            y += 15

            # إضافة التوقيع والتاريخ
            current_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # تاريخ الطباعة
            pdf.set_detail_font(10)
            pdf.set_xy(10, y)
            pdf.cell(95, 8, pdf.ar_text(f'تاريخ الطباعة: {current_date}'), border=0, align='R')

            # مساحة للتوقيع
            signature_width = 95
            pdf.set_xy(105, y + 10)
            pdf.cell(signature_width, 15, '', border=1, align='C')

            pdf.set_xy(105, y + 27)
            pdf.cell(signature_width, 8, pdf.ar_text('توقيع المسؤول'), border=0, align='C')

            # حفظ الملف
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            safe_title = title.replace("تقرير المهام حسب النوع", "type_report").replace("تقرير المهام حسب الأولوية", "priority_report").replace("تقرير المهام حسب الحالة", "status_report")
            filename = os.path.join(reports_dir, f"{safe_title}_{timestamp}.pdf")
            pdf.output(filename)

            print(f"✅ [SUCCESS] تم إنشاء التقرير: {filename}")
            QMessageBox.information(self, "نجح", f"تم إنشاء التقرير بنجاح:\n{filename}")

            # فتح الملف
            try:
                os.startfile(filename)
            except Exception as e:
                print(f"⚠️ فشل في فتح الملف: {e}")
                QMessageBox.information(self, "تنبيه", f"تم إنشاء التقرير في:\n{filename}")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء تقرير الملخص: {str(e)}")
            traceback.print_exc()
            raise

    def create_comprehensive_pdf_report(self, tasks):
        """إنشاء تقرير شامل PDF بتصميم أنيق مثل print111.py"""
        try:
            from fpdf import FPDF
            import arabic_reshaper
            from bidi.algorithm import get_display

            class ArabicPDF(FPDF):
                def __init__(self):
                    super().__init__('P','mm','A4')
                    self.set_margins(10, 10, 10)
                    self.set_auto_page_break(auto=True, margin=20)
                    fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')

                    calibri_path = os.path.join(fonts_dir, 'calibri.ttf')
                    calibri_bold_path = os.path.join(fonts_dir, 'calibrib.ttf')
                    arial_path = os.path.join(fonts_dir, 'arial.ttf')
                    arial_bold_path = os.path.join(fonts_dir, 'arialbd.ttf')

                    if os.path.exists(calibri_path):
                        self.add_font('Calibri', '', calibri_path)
                        self.calibri_available = True
                    else:
                        self.calibri_available = False

                    if os.path.exists(calibri_bold_path):
                        self.add_font('Calibri', 'B', calibri_bold_path)
                        self.calibri_bold_available = True
                    else:
                        self.calibri_bold_available = False

                    if os.path.exists(arial_path):
                        self.add_font('Arial', '', arial_path)
                    if os.path.exists(arial_bold_path):
                        self.add_font('Arial', 'B', arial_bold_path)

                def set_title_font(self, size=14):
                    """خط العناوين - Calibri 14 أزرق غامق"""
                    if self.calibri_bold_available:
                        self.set_font('Calibri', 'B', size)
                    else:
                        self.set_font('Arial', 'B', size)

                def set_detail_font(self, size=13):
                    """خط الجداول - Calibri 13 أسود غامق"""
                    if self.calibri_bold_available:
                        self.set_font('Calibri', 'B', size)
                    else:
                        self.set_font('Arial', 'B', size)

                def ar_text(self, txt: str) -> str:
                    reshaped = arabic_reshaper.reshape(str(txt))
                    return get_display(reshaped)

            # الحصول على بيانات المؤسسة
            logo_path, institution_name = self.get_institution_data()

            # إنشاء مجلد التقارير على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop')
            reports_dir = os.path.join(desktop_path, 'تقارير المهام والمواعيد')
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)

            # إنشاء ملف PDF
            pdf = ArabicPDF()
            pdf.add_page()
            y = pdf.get_y()

            # إضافة الشعار
            if logo_path:
                logo_w, logo_h = 60, 24
                x_logo = (pdf.w - logo_w) / 2
                pdf.image(logo_path, x=x_logo, y=y, w=logo_w, h=logo_h)
                y += logo_h + 5

            # اسم المؤسسة
            pdf.set_title_font(14)  # Calibri 14 أزرق غامق
            pdf.set_text_color(0, 51, 102)
            pdf.set_xy(10, y)
            pdf.cell(0, 10, pdf.ar_text(institution_name), border=0, align='C')
            y += 15

            # عنوان التقرير
            title = "التقرير الشامل للمهام والمواعيد"
            pdf.set_title_font(14)  # Calibri 14 أزرق غامق
            pdf.set_text_color(0, 51, 102)
            pdf.set_xy(10, y)
            pdf.cell(0, 12, pdf.ar_text(title), border=1, align='C')
            pdf.set_text_color(0, 0, 0)
            y += 17

            # عدد المهام
            count_text = f"إجمالي عدد المهام: {len(tasks)}"
            pdf.set_title_font(13)  # Calibri 13 أزرق غامق
            pdf.set_text_color(0, 51, 102)
            pdf.set_xy(10, y)
            pdf.cell(0, 10, pdf.ar_text(count_text), border=0, align='C')
            y += 15

            # تفاصيل كل مهمة
            pdf.set_text_color(0, 0, 0)

            for i, task in enumerate(tasks, 1):
                # التحقق من الحاجة لصفحة جديدة
                if y > pdf.h - 50:
                    pdf.add_page()
                    y = pdf.get_y()

                # عنوان المهمة
                task_title = f"المهمة {i}: {task[0]}"
                pdf.set_title_font(14)  # Calibri 14 أزرق غامق
                pdf.set_text_color(0, 51, 102)
                pdf.set_xy(10, y)
                pdf.cell(0, 8, pdf.ar_text(task_title), border=0)
                y += 10

                # تفاصيل المهمة
                pdf.set_detail_font(13)  # Calibri 13 أسود غامق
                pdf.set_text_color(0, 0, 0)

                details = [
                    f"الوصف: {task[1] or 'غير محدد'}",
                    f"النوع: {task[2]}",
                    f"الأولوية: {task[3]}",
                    f"الحالة: {task[4]}",
                    f"تاريخ البداية: {task[5]}",
                    f"تاريخ النهاية: {task[6] or 'غير محدد'}",
                    f"وقت البداية: {task[7] or 'غير محدد'}",
                    f"وقت النهاية: {task[8] or 'غير محدد'}",
                    f"المكان: {task[9] or 'غير محدد'}",
                    f"الحضور: {task[10] or 'غير محدد'}"
                ]

                for detail in details:
                    pdf.set_xy(10, y)
                    pdf.cell(0, 6, pdf.ar_text(detail), border=0)
                    y += 6

                y += 10

            # إضافة التوقيع والتاريخ في النهاية
            if y > pdf.h - 40:
                pdf.add_page()
                y = pdf.get_y()

            y += 10
            current_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # تاريخ الطباعة
            pdf.set_detail_font(10)
            pdf.set_xy(10, y)
            pdf.cell(95, 8, pdf.ar_text(f'تاريخ الطباعة: {current_date}'), border=0, align='R')

            # مساحة للتوقيع
            signature_width = 95
            pdf.set_xy(105, y + 10)
            pdf.cell(signature_width, 15, '', border=1, align='C')

            pdf.set_xy(105, y + 27)
            pdf.cell(signature_width, 8, pdf.ar_text('توقيع المسؤول'), border=0, align='C')

            # حفظ الملف
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = os.path.join(reports_dir, f"comprehensive_report_{timestamp}.pdf")
            pdf.output(filename)

            print(f"✅ [SUCCESS] تم إنشاء التقرير الشامل: {filename}")
            QMessageBox.information(self, "نجح", f"تم إنشاء التقرير الشامل بنجاح:\n{filename}")

            # فتح الملف
            try:
                os.startfile(filename)
            except Exception as e:
                print(f"⚠️ فشل في فتح الملف: {e}")
                QMessageBox.information(self, "تنبيه", f"تم إنشاء التقرير في:\n{filename}")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء التقرير الشامل: {str(e)}")
            traceback.print_exc()
            raise


def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    window = TasksAndAppointmentsWindow()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
